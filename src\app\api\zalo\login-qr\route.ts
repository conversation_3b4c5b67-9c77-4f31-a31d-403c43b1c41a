import { NextRequest, NextResponse } from 'next/server';
import path from 'node:path';
import fs from 'node:fs/promises';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// L<PERSON>u trạng thái phiên đăng nhập QR tạm thời trong bộ nhớ tiến trình
// Chỉ dùng cho môi trường dev/self-host. Với serverless, cần thay bằng store bền vững (Redis,...)
const sessions: Map<string, { done: boolean; ok?: boolean; error?: string; cookiePath?: string; accountPath?: string; qrPath?: string }> = new Map();

async function ensureTmpDir(): Promise<string> {
  const dir = path.join(process.cwd(), 'tmp');
  await fs.mkdir(dir, { recursive: true });
  return dir;
}

async function ensureSessionDir(sessionId: string): Promise<string> {
  const base = await ensureTmpDir();
  const sessionDir = path.join(base, sessionId);
  await fs.mkdir(sessionDir, { recursive: true });
  return sessionDir;
}

async function saveBase64Image(base64String: string, outputPath: string): Promise<void> {
  let base64Data = base64String;
  const matches = /^data:(image\/\w+);base64,(.+)$/.exec(base64String);
  if (matches) {
    base64Data = matches[2];
  }
  await fs.mkdir(path.dirname(outputPath), { recursive: true });
  await fs.writeFile(outputPath, Buffer.from(base64Data, 'base64'));
}

// Khởi tạo login bằng QR, trả về ảnh QR (base64) và sessionId để FE poll trạng thái
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const userAgent: string = body?.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36';
    // Một số phiên bản ZCA yêu cầu có imei trong constructor
    const imei: string = body?.imei || `${Math.floor(1e14 + Math.random() * 9e14)}`; // 15 digits fallback
    const saveCookie: boolean = body?.saveCookie !== false; // mặc định lưu cookie

    // Dynamic import giống như logic ở login/route.ts
    let Zalo: any;
    try {
      const zcaModule: any = await import('zca-js');
      // Thử tìm constructor Zalo/ZCA theo nhiều cách
      const candidates: any[] = [
        zcaModule?.Zalo,
        zcaModule?.ZCA,
        zcaModule?.default?.Zalo,
        zcaModule?.default?.ZCA,
        zcaModule?.default,
        zcaModule
      ].filter(Boolean);
      let picked: any = candidates.find((c: any) => typeof c === 'function');
      if (!picked && zcaModule && typeof zcaModule === 'object') {
        for (const key of Object.keys(zcaModule)) {
          const val: any = (zcaModule as any)[key];
          if (typeof val === 'function' && /Zalo|ZCA/i.test(val.name || key)) { picked = val; break; }
        }
      }
      Zalo = picked;
    } catch (importError) {
      console.error('Failed to import zca-js:', importError);
      return NextResponse.json(
        { error: 'Không thể load thư viện Zalo. Vui lòng kiểm tra cài đặt.' },
        { status: 500 }
      );
    }

    // Truyền đầy đủ các option an toàn cho nhiều phiên bản
    // Một số phiên bản ZCA khác nhau có constructor yêu cầu khác nhau.
    // Cho phép client truyền config (tương tự global.config.zca_js_config)
    const zcaConfig = body?.zcaConfig || body?.zca_js_config || body?.config || {};
    let zalo: any = null;
    const ctorErrors: string[] = [];
    if (typeof Zalo === 'function') {
      const attempts: Array<() => any> = [
        () => new Zalo(zcaConfig),
        () => new Zalo({ imei, userAgent, ...zcaConfig }),
        () => new Zalo(imei, userAgent),
        () => new Zalo(userAgent, imei),
        () => new Zalo(),
        () => new Zalo({})
      ];
      for (const tryCtor of attempts) {
        try {
          zalo = tryCtor();
          if (zalo) break;
        } catch (e: any) {
          ctorErrors.push(e?.message || String(e));
        }
      }
    }
    if (!zalo) {
      const detail = ctorErrors.filter(Boolean).slice(0, 2).join(' | ');
      return NextResponse.json({ error: 'Không thể khởi tạo Zalo (ZCA). Phiên bản thư viện có thể khác yêu cầu.', details: detail || 'No suitable constructor found' }, { status: 500 });
    }

    const sessionId = (globalThis.crypto as any)?.randomUUID?.() || `${Date.now()}-${Math.random()}`;
    const dir = await ensureTmpDir();
    const sessionDir = await ensureSessionDir(sessionId);
    const qrPath = path.join(dir, `qr_${sessionId}.png`);
    const cookiePath = path.join(sessionDir, 'cookie.json');
    const accountPath = path.join(sessionDir, 'account.json');

    // Bắt đầu luồng đăng nhập ở background
    (async () => {
      try {
        let api: any;
        const handleCallback = async (qrData: any) => {
          try {
            const data = qrData?.data || {};
            const { image, cookie, imei: cbImei, userAgent: cbUA } = data;
            if (image && !cookie) {
              await saveBase64Image(image, qrPath);
              sessions.set(sessionId, { done: false, ok: undefined, qrPath });
              return;
            }
            if (cbUA && cookie && cbImei) {
              if (saveCookie) {
                try {
                  await fs.writeFile(cookiePath, JSON.stringify(cookie, null, 2), 'utf8');
                  const newAccountData = { imei: cbImei, userAgent: cbUA, cookie: 'cookie.json' };
                  await fs.writeFile(accountPath, JSON.stringify(newAccountData, null, 2), 'utf8');
                  sessions.set(sessionId, { done: true, ok: true, cookiePath, accountPath, qrPath });
                } catch (err: any) {
                  sessions.set(sessionId, { done: true, ok: false, error: err?.message || String(err), qrPath });
                }
              } else {
                sessions.set(sessionId, { done: true, ok: true, qrPath });
              }
            }
          } catch (e: any) {
            sessions.set(sessionId, { done: true, ok: false, error: e?.message || 'QR callback failed', qrPath });
          }
        };

        if (typeof (zalo as any).loginQR === 'function') {
          // Thử dạng hỗ trợ callback (options, callback)
          let usedCallback = false;
          try {
            if ((zalo as any).loginQR.length >= 2) {
              usedCallback = true;
              api = await (zalo as any).loginQR({}, handleCallback);
            }
          } catch {}
          if (!api) {
            // Fallback: ghi QR ra file nếu lib hỗ trợ
            api = await (zalo as any).loginQR({ userAgent, qrPath });
          }
        } else if (typeof (Zalo as any).loginQR === 'function') {
          // một số bản cung cấp static loginQR
          api = await (Zalo as any).loginQR({ userAgent, qrPath });
        } else {
          throw new Error('Thư viện không hỗ trợ loginQR trong phiên bản hiện tại');
        }

        if (api?.listener?.start) api.listener.start();
        // Nếu không có callback, đánh dấu là đang chờ user quét
        const current = sessions.get(sessionId);
        if (!current) {
          sessions.set(sessionId, { done: false, ok: undefined, qrPath });
        }
      } catch (e: any) {
        sessions.set(sessionId, { done: true, ok: false, error: e?.message || 'LoginQR failed' });
      }
    })();

    // Poll một chút để QR được ghi file, nếu chưa có thì trả null; FE sẽ gọi lại
    let qrBase64: string | null = null;
    try {
      const buf = await fs.readFile(qrPath).catch(async () => {
        // chờ file xuất hiện trong thời gian ngắn
        await new Promise(r => setTimeout(r, 300));
        try { return await fs.readFile(qrPath); } catch { return null as any; }
      });
      qrBase64 = buf ? `data:image/png;base64,${buf.toString('base64')}` : null;
    } catch { qrBase64 = null; }

    sessions.set(sessionId, { ...(sessions.get(sessionId) || { done: false }), qrPath });
    return NextResponse.json({ sessionId, qrBase64, qrPath });
  } catch (error: any) {
    console.error('QR start error:', error);
    if (error?.message && error.message.includes('sharp')) {
      return NextResponse.json(
        {
          error: 'Lỗi thư viện Sharp. Vui lòng cài đặt lại: npm install --include=optional sharp',
          details: 'Sharp module không thể load trên Windows. Hãy thử cài đặt lại.'
        },
        { status: 500 }
      );
    }
    return NextResponse.json({ error: error?.message || 'Cannot start QR login' }, { status: 500 });
  }
}

// Lấy trạng thái đăng nhập của một sessionId
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const sessionId = searchParams.get('sessionId') || '';
  const status = sessions.get(sessionId) || { done: false };
  return NextResponse.json(status);
}


