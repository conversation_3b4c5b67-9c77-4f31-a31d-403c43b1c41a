import { NextRequest, NextResponse } from 'next/server';
import path from 'node:path';
import fs from 'node:fs/promises';
import fsSync from 'node:fs';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// Logger utility
const logger = {
  log: (message: string, level: 'info' | 'warn' | 'error' = 'info') => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }
};

// Lưu trạng thái phiên đăng nhập QR tạm thời trong bộ nhớ tiến trình
// Chỉ dùng cho môi trường dev/self-host. Với serverless, cần thay bằng store bền vững (Redis,...)
const sessions: Map<string, { done: boolean; ok?: boolean; error?: string; cookiePath?: string; accountPath?: string; qrPath?: string }> = new Map();

async function ensureTmpDir(): Promise<string> {
  const dir = path.join(process.cwd(), 'tmp');
  await fs.mkdir(dir, { recursive: true });
  return dir;
}

async function ensureSessionDir(sessionId: string): Promise<string> {
  const base = await ensureTmpDir();
  const sessionDir = path.join(base, sessionId);
  await fs.mkdir(sessionDir, { recursive: true });
  return sessionDir;
}

function saveBase64Image(base64String: string, outputPath: string): void {
  const matches = base64String.match(/^data:(image\/\w+);base64,(.+)$/);
  let base64Data = base64String;

  if (matches) {
    base64Data = matches[2];
  }

  fsSync.mkdirSync(path.dirname(outputPath), { recursive: true });
  fsSync.writeFileSync(outputPath, Buffer.from(base64Data, 'base64'));
}

const getJsonData = (filePath: string, defaultData: any = {}): any => {
  fsSync.mkdirSync(path.dirname(filePath), { recursive: true });

  if (!fsSync.existsSync(filePath)) {
    logger.log(`File ${path.basename(filePath)} chưa tồn tại, tạo mới.`, "warn");
    fsSync.writeFileSync(filePath, JSON.stringify(defaultData, null, 2), "utf8");
    return defaultData;
  }

  const raw = fsSync.readFileSync(filePath, "utf8");
  return JSON.parse(raw);
};

// Utility function similar to your sample loginWithQR
async function loginWithQR(config: {
  zcaConfig?: any;
  accountPath: string;
  qrcodePath: string;
  saveCookie?: boolean;
  userAgent?: string;
  imei?: string;
}): Promise<any> {
  try {
    // Dynamic import ZCA
    const zcaModule: any = await import('zca-js');
    const candidates: any[] = [
      zcaModule?.Zalo,
      zcaModule?.ZCA,
      zcaModule?.default?.Zalo,
      zcaModule?.default?.ZCA,
      zcaModule?.default,
      zcaModule
    ].filter(Boolean);

    let Zalo: any = candidates.find((c: any) => typeof c === 'function');
    if (!Zalo && zcaModule && typeof zcaModule === 'object') {
      for (const key of Object.keys(zcaModule)) {
        const val: any = (zcaModule as any)[key];
        if (typeof val === 'function' && /Zalo|ZCA/i.test(val.name || key)) {
          Zalo = val;
          break;
        }
      }
    }

    if (!Zalo) {
      throw new Error('Không thể tìm thấy constructor Zalo/ZCA');
    }

    const zalo = new Zalo(config.zcaConfig || {});
    fsSync.mkdirSync(path.dirname(config.accountPath), { recursive: true });

    const accountData = getJsonData(config.accountPath);
    const cookieFileName = accountData.cookie || "cookie.json";
    const cookiePath = path.join(path.dirname(config.accountPath), cookieFileName);

    const api = await zalo.loginQR({}, (qrData: any) => {
      const { image, cookie, imei, userAgent } = qrData.data;

      if (image && !cookie) {
        saveBase64Image(image, config.qrcodePath);
        logger.log(`Vui lòng quét mã QRCode ${path.basename(config.qrcodePath)} để đăng nhập`, "info");
        return;
      }

      if (userAgent && cookie && imei) {
        if (config.saveCookie !== false) {
          try {
            fsSync.writeFileSync(cookiePath, JSON.stringify(cookie, null, 2), "utf8");

            const newAccountData = {
              imei,
              userAgent,
              cookie: cookieFileName
            };
            fsSync.writeFileSync(config.accountPath, JSON.stringify(newAccountData, null, 2), "utf8");

            logger.log(`Đã lưu cookie vào ${cookieFileName} và cập nhật ${path.basename(config.accountPath)}`, "info");
          } catch (err: any) {
            logger.log(`Lỗi khi ghi file: ${err?.message || err}`, "error");
            throw err;
          }
        }
      }
    });

    return api;
  } catch (error: any) {
    logger.log(`Lỗi đăng nhập Zalo bằng QR: ${error?.message || error}`, "error");
    throw error;
  }
}

// Khởi tạo login bằng QR, trả về ảnh QR (base64) và sessionId để FE poll trạng thái
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const userAgent: string = body?.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36';
    // Một số phiên bản ZCA yêu cầu có imei trong constructor
    const imei: string = body?.imei || `${Math.floor(1e14 + Math.random() * 9e14)}`; // 15 digits fallback
    const saveCookie: boolean = body?.saveCookie !== false; // mặc định lưu cookie

    // Dynamic import giống như logic ở login/route.ts
    let Zalo: any;
    try {
      const zcaModule: any = await import('zca-js');
      // Thử tìm constructor Zalo/ZCA theo nhiều cách
      const candidates: any[] = [
        zcaModule?.Zalo,
        zcaModule?.ZCA,
        zcaModule?.default?.Zalo,
        zcaModule?.default?.ZCA,
        zcaModule?.default,
        zcaModule
      ].filter(Boolean);
      let picked: any = candidates.find((c: any) => typeof c === 'function');
      if (!picked && zcaModule && typeof zcaModule === 'object') {
        for (const key of Object.keys(zcaModule)) {
          const val: any = (zcaModule as any)[key];
          if (typeof val === 'function' && /Zalo|ZCA/i.test(val.name || key)) { picked = val; break; }
        }
      }
      Zalo = picked;
    } catch (importError) {
      console.error('Failed to import zca-js:', importError);
      return NextResponse.json(
        { error: 'Không thể load thư viện Zalo. Vui lòng kiểm tra cài đặt.' },
        { status: 500 }
      );
    }

    // Truyền đầy đủ các option an toàn cho nhiều phiên bản
    // Một số phiên bản ZCA khác nhau có constructor yêu cầu khác nhau.
    // Cho phép client truyền config (tương tự global.config.zca_js_config)
    const zcaConfig = body?.zcaConfig || body?.zca_js_config || body?.config || {};
    let zalo: any = null;
    const ctorErrors: string[] = [];
    if (typeof Zalo === 'function') {
      const attempts: Array<() => any> = [
        () => new Zalo(zcaConfig),
        () => new Zalo({ imei, userAgent, ...zcaConfig }),
        () => new Zalo(imei, userAgent),
        () => new Zalo(userAgent, imei),
        () => new Zalo(),
        () => new Zalo({})
      ];
      for (const tryCtor of attempts) {
        try {
          zalo = tryCtor();
          if (zalo) break;
        } catch (e: any) {
          ctorErrors.push(e?.message || String(e));
        }
      }
    }
    if (!zalo) {
      const detail = ctorErrors.filter(Boolean).slice(0, 2).join(' | ');
      return NextResponse.json({ error: 'Không thể khởi tạo Zalo (ZCA). Phiên bản thư viện có thể khác yêu cầu.', details: detail || 'No suitable constructor found' }, { status: 500 });
    }

    const sessionId = (globalThis.crypto as any)?.randomUUID?.() || `${Date.now()}-${Math.random()}`;
    const dir = await ensureTmpDir();
    const sessionDir = await ensureSessionDir(sessionId);
    const qrPath = path.join(dir, `qr_${sessionId}.png`);
    const cookiePath = path.join(sessionDir, 'cookie.json');
    const accountPath = path.join(sessionDir, 'account.json');

    // Bắt đầu luồng đăng nhập ở background
    (async () => {
      try {
        let api: any;
        const handleCallback = async (qrData: any) => {
          try {
            const data = qrData?.data || {};
            const { image, cookie, imei: cbImei, userAgent: cbUA } = data;
            if (image && !cookie) {
              saveBase64Image(image, qrPath);
              logger.log(`Vui lòng quét mã QRCode ${path.basename(qrPath)} để đăng nhập`, "info");
              sessions.set(sessionId, { done: false, ok: undefined, qrPath });
              return;
            }
            if (cbUA && cookie && cbImei) {
              if (saveCookie) {
                try {
                  fsSync.writeFileSync(cookiePath, JSON.stringify(cookie, null, 2), "utf8");

                  const newAccountData = {
                    imei: cbImei,
                    userAgent: cbUA,
                    cookie: "cookie.json"
                  };
                  fsSync.writeFileSync(accountPath, JSON.stringify(newAccountData, null, 2), "utf8");

                  logger.log(`Đã lưu cookie vào cookie.json và cập nhật ${path.basename(accountPath)}`, "info");
                  sessions.set(sessionId, { done: true, ok: true, cookiePath, accountPath, qrPath });
                } catch (err: any) {
                  logger.log(`Lỗi khi ghi file: ${err?.message || err}`, "error");
                  sessions.set(sessionId, { done: true, ok: false, error: err?.message || String(err), qrPath });
                }
              } else {
                sessions.set(sessionId, { done: true, ok: true, qrPath });
              }
            }
          } catch (e: any) {
            sessions.set(sessionId, { done: true, ok: false, error: e?.message || 'QR callback failed', qrPath });
          }
        };

        if (typeof (zalo as any).loginQR === 'function') {
          // Thử dạng hỗ trợ callback (options, callback)
          try {
            if ((zalo as any).loginQR.length >= 2) {
              api = await (zalo as any).loginQR({}, handleCallback);
            }
          } catch {}
          if (!api) {
            // Fallback: ghi QR ra file nếu lib hỗ trợ
            api = await (zalo as any).loginQR({ userAgent, qrPath });
          }
        } else if (typeof (Zalo as any).loginQR === 'function') {
          // một số bản cung cấp static loginQR
          api = await (Zalo as any).loginQR({ userAgent, qrPath });
        } else {
          throw new Error('Thư viện không hỗ trợ loginQR trong phiên bản hiện tại');
        }

        if (api?.listener?.start) api.listener.start();
        // Nếu không có callback, đánh dấu là đang chờ user quét
        const current = sessions.get(sessionId);
        if (!current) {
          sessions.set(sessionId, { done: false, ok: undefined, qrPath });
        }
      } catch (e: any) {
        sessions.set(sessionId, { done: true, ok: false, error: e?.message || 'LoginQR failed' });
      }
    })();

    // Poll một chút để QR được ghi file, nếu chưa có thì trả null; FE sẽ gọi lại
    let qrBase64: string | null = null;
    try {
      const buf = await fs.readFile(qrPath).catch(async () => {
        // chờ file xuất hiện trong thời gian ngắn
        await new Promise(r => setTimeout(r, 300));
        try { return await fs.readFile(qrPath); } catch { return null as any; }
      });
      qrBase64 = buf ? `data:image/png;base64,${buf.toString('base64')}` : null;
    } catch { qrBase64 = null; }

    sessions.set(sessionId, { ...(sessions.get(sessionId) || { done: false }), qrPath });
    return NextResponse.json({ sessionId, qrBase64, qrPath });
  } catch (error: any) {
    console.error('QR start error:', error);
    if (error?.message && error.message.includes('sharp')) {
      return NextResponse.json(
        {
          error: 'Lỗi thư viện Sharp. Vui lòng cài đặt lại: npm install --include=optional sharp',
          details: 'Sharp module không thể load trên Windows. Hãy thử cài đặt lại.'
        },
        { status: 500 }
      );
    }
    return NextResponse.json({ error: error?.message || 'Cannot start QR login' }, { status: 500 });
  }
}

// Lấy trạng thái đăng nhập của một sessionId
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const sessionId = searchParams.get('sessionId') || '';
  const status = sessions.get(sessionId) || { done: false };
  return NextResponse.json(status);
}


